"""
Excel数据处理工具 - FME集成版本

功能：将源Excel文件的A-I列数据（除第一行）按模板格式复制到新文件中
支持区县算法和乡镇算法

FME参数：
- a: 算法选择（1=区县算法, 2=乡镇算法）
- path_windows: 源Excel文件路径
- new_path: 输出Excel文件路径
- 模板_path: 模板Excel文件路径
"""

import fme 
import fmeobjects
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
import os
import shutil


class FeatureProcessor(object):
    """Excel处理工具的FME集成版本
    支持区县算法和乡镇算法的Excel数据处理
    """

    def __init__(self):
        """Base constructor for class members."""
        pass
        
    def has_support_for(self, support_type: int):
        """This method is called by FME to determine if the PythonCaller supports Bulk mode,
        which allows for significant performance gains when processing large numbers of features.
        Bulk mode cannot always be supported. 
        More information available in transformer help.
        """
        return support_type == fmeobjects.FME_SUPPORT_FEATURE_TABLE_SHIM
  
    def input(self, feature: fmeobjects.FMEFeature):
        """处理每个输入的FME Feature
        从feature中获取参数并执行Excel处理
        """
        try:
            print("📊 FME Excel处理工具开始运行")

            # 从feature获取参数
            a = int(feature.getAttribute('a'))  # 算法选择：1=区县算法, 2=乡镇算法
            path_windows = feature.getAttribute('path_windows')  # 源文件路径
            new_path = feature.getAttribute('new_path')  # 输出文件路径
            muban_path = feature.getAttribute('模板_path')  # 模板文件路径

            # 规范化路径，处理双反斜杠等问题
            if path_windows:
                # 处理多种路径问题：双反斜杠、多余的反斜杠等
                path_windows = path_windows.replace('\\\\', '\\').replace('///', '/').replace('//', '/')
                path_windows = os.path.normpath(path_windows)
                print(f"🔧 规范化后的源文件路径: {path_windows}")
            if new_path:
                new_path = new_path.replace('\\\\', '\\').replace('///', '/').replace('//', '/')
                new_path = os.path.normpath(new_path)
                print(f"🔧 规范化后的输出文件路径: {new_path}")
            if muban_path:
                muban_path = muban_path.replace('\\\\', '\\').replace('///', '/').replace('//', '/')
                muban_path = os.path.normpath(muban_path)
                print(f"🔧 规范化后的模板文件路径: {muban_path}")

            print(f"📋 获取到参数:")
            print(f"   算法选择: a={a} ({'区县算法' if a == 1 else '乡镇算法' if a == 2 else '未知算法'})")
            print(f"   源文件: {path_windows}")
            print(f"   模板文件: {muban_path}")
            print(f"   输出文件: {new_path}")

            # 验证参数
            if not all([path_windows, new_path, muban_path]):
                print("❌ 错误：缺少必要的路径参数")
                feature.setAttribute('处理状态', '失败')
                feature.setAttribute('错误信息', '缺少必要的路径参数')
                self.pyoutput(feature)
                return

            if a not in [1, 2]:
                print(f"❌ 错误：无效的算法选择 a={a}")
                feature.setAttribute('处理状态', '失败')
                feature.setAttribute('错误信息', f'无效的算法选择: a={a}')
                self.pyoutput(feature)
                return

            # 验证文件是否存在
            if path_windows and not os.path.exists(path_windows):
                print(f"❌ 错误：源文件不存在: {path_windows}")
                # 尝试列出目录内容以帮助调试
                parent_dir = os.path.dirname(path_windows)
                if os.path.exists(parent_dir):
                    print(f"📁 父目录存在，内容如下:")
                    try:
                        for item in os.listdir(parent_dir):
                            print(f"   - {item}")
                    except Exception as e:
                        print(f"   无法列出目录内容: {e}")
                else:
                    print(f"📁 父目录也不存在: {parent_dir}")
                feature.setAttribute('处理状态', '失败')
                feature.setAttribute('错误信息', f'源文件不存在: {path_windows}')
                self.pyoutput(feature)
                return

            if muban_path and not os.path.exists(muban_path):
                print(f"❌ 错误：模板文件不存在: {muban_path}")
                feature.setAttribute('处理状态', '失败')
                feature.setAttribute('错误信息', f'模板文件不存在: {muban_path}')
                self.pyoutput(feature)
                return

            print("🔄 开始执行Excel处理...")

            # 执行Excel处理
            if a == 1:
                print("🏙️ 执行区县算法")
                result = self.process_county_algorithm(path_windows, muban_path, new_path)
            elif a == 2:
                print("🏘️ 执行乡镇算法")
                result = self.process_township_algorithm(path_windows, muban_path, new_path)

            # 设置处理结果到feature
            if result['success']:
                print(f"🎉 处理成功！")
                print(f"📈 处理了 {result['rows_processed']} 行数据")
                print(f"💾 输出文件: {new_path}")
                feature.setAttribute('处理状态', '成功')
                feature.setAttribute('处理行数', result['rows_processed'])
                feature.setAttribute('输出文件', new_path)
                feature.setAttribute('算法类型', '区县算法' if a == 1 else '乡镇算法')
            else:
                print(f"❌ 处理失败：{result['message']}")
                feature.setAttribute('处理状态', '失败')
                feature.setAttribute('错误信息', result['message'])

        except Exception as e:
            print(f"❌ 处理异常：{str(e)}")
            feature.setAttribute('处理状态', '失败')
            feature.setAttribute('错误信息', f'处理异常: {str(e)}')

        self.pyoutput(feature)

    def close(self):
        """This method is called once all the FME Features have been processed from input()."""
        pass

    def process_group(self):
        """This method is called by FME for each group when group processing mode is enabled."""
        pass

    def process_county_algorithm(self, source_path, template_path, output_path):
        """区县算法：处理单个工作表"""
        try:
            print("🏙️ 开始区县算法处理...")

            # 再次规范化路径（防止传入时仍有问题）
            source_path = os.path.normpath(source_path.replace('\\\\', '\\'))
            template_path = os.path.normpath(template_path.replace('\\\\', '\\'))
            output_path = os.path.normpath(output_path.replace('\\\\', '\\'))

            print(f"🔧 最终源文件路径: {source_path}")
            print(f"🔧 最终模板文件路径: {template_path}")
            print(f"🔧 最终输出文件路径: {output_path}")

            # 验证文件存在性
            if not os.path.exists(source_path):
                print(f"❌ 源文件不存在: {source_path}")
                return {'success': False, 'message': f'源文件不存在: {source_path}', 'rows_processed': 0}

            if not os.path.exists(template_path):
                print(f"❌ 模板文件不存在: {template_path}")
                return {'success': False, 'message': f'模板文件不存在: {template_path}', 'rows_processed': 0}

            # 读取源文件数据
            print(f"📖 正在读取源文件: {os.path.basename(source_path)}")
            source_wb = openpyxl.load_workbook(source_path, data_only=True)
            source_ws = source_wb.active
            
            # 读取A-I列的数据（从第2行开始，跳过第1行）
            print(f"📋 源文件工作表名称: {source_ws.title}")
            print(f"📊 源文件总行数: {source_ws.max_row}")

            data_rows = []
            max_row = source_ws.max_row

            for row in range(2, max_row + 1):  # 从第2行开始
                row_data = []
                has_data = False

                for col in range(1, 10):  # A-I列 (1-9)
                    cell = source_ws.cell(row=row, column=col)
                    cell_value = cell.value
                    row_data.append(cell_value)

                    if cell_value is not None and str(cell_value).strip():
                        has_data = True

                # 只添加有数据的行
                if has_data:
                    data_rows.append(row_data)

            print(f"📈 从源文件读取了 {len(data_rows)} 行有效数据")

            if not data_rows:
                print("❌ 源文件中没有找到有效数据")
                source_wb.close()
                return {'success': False, 'message': '源文件中没有找到有效数据', 'rows_processed': 0}
            
            # 创建市级代码到名称的映射
            city_code_map = {
                '3201': '南京市', '3202': '无锡市', '3203': '徐州市', '3204': '常州市',
                '3205': '苏州市', '3206': '南通市', '3207': '连云港市', '3208': '淮安市',
                '3209': '盐城市', '3210': '扬州市', '3211': '镇江市', '3212': '泰州市',
                '3213': '宿迁市'
            }
            
            # 初始化区县名称和代码（无默认值）
            county_name = None
            city_name = None

            # 从源文件第一行（表头）识别列名
            header_row = []
            for col in range(1, 12):  # A-K列（扩展到K列）
                cell = source_ws.cell(row=1, column=col)
                header_value = cell.value
                header_row.append(str(header_value).strip() if header_value else "")

            print(f"🔍 源文件表头: {header_row}")

            # 查找区县名称列和区县代码列的索引（明确指定J列和K列）
            county_name_col_idx = 10  # K列（第11列，索引为10）
            county_code_col_idx = 9   # J列（第10列，索引为9）
            
            print(f"🏘️ 使用K列作为区县名称列: 第{county_name_col_idx+1}列")
            print(f"🏙️ 使用J列作为区县代码列: 第{county_code_col_idx+1}列")

            # 从数据中提取市县信息
            if data_rows:
                # 需要重新读取包含J、K列的数据
                extended_first_row = []
                for col in range(1, 12):  # A-K列
                    cell = source_ws.cell(row=2, column=col)  # 第2行数据
                    extended_first_row.append(cell.value)

                # 从K列获取县名
                if county_name_col_idx < len(extended_first_row):
                    name_value = extended_first_row[county_name_col_idx]
                    if name_value and isinstance(name_value, str):
                        county_name = name_value.strip()
                        print(f"🏘️ 从K列获取县名: {county_name}")

                # 从J列获取市名
                if county_code_col_idx < len(extended_first_row):
                    code_value = extended_first_row[county_code_col_idx]
                    if code_value:
                        county_code = str(code_value).strip()
                        if len(county_code) >= 4:
                            city_code = county_code[:4]
                            if city_code in city_code_map:
                                city_name = city_code_map[city_code]
                                print(f"🏙️ 从J列区县代码 {county_code} 反推市名: {city_name}")

            # 验证是否成功获取到市县信息
            if not county_name or not city_name:
                print("⚠️ 警告：未能从数据中提取到完整的市县信息")
                if not county_name:
                    print("❌ 缺少县名称信息")
                if not city_name:
                    print("❌ 缺少市名称信息")
                return {'success': False, 'message': '无法从数据中提取市县信息', 'rows_processed': 0}

            print(f"🏙️ 最终市名称: {city_name}")
            print(f"🏘️ 最终县名称: {county_name}")

            # 检查输出文件是否存在，如果不存在则创建
            if not os.path.exists(output_path):
                # 创建输出目录
                output_dir = os.path.dirname(output_path)
                if output_dir and not os.path.exists(output_dir):
                    os.makedirs(output_dir)
                    print(f"✓ 创建输出目录: {output_dir}")

                # 复制模板文件到输出位置
                shutil.copy2(template_path, output_path)
                print(f"✓ 已复制模板文件到: {output_path}")

            # 打开目标文件
            target_wb = openpyxl.load_workbook(output_path)

            # 设置工作表名称
            source_sheet_name = source_ws.title
            # 算法1：工作表名称使用原工作簿名称，而不是特殊的汇总名称
            target_sheet_name = source_sheet_name

            # 检查是否已存在同名工作表
            if target_sheet_name in target_wb.sheetnames:
                print(f"📋 工作表 '{target_sheet_name}' 已存在，将覆盖")
                target_ws = target_wb[target_sheet_name]
            else:
                # 创建新的工作表或使用现有的第一个工作表
                if len(target_wb.worksheets) == 1 and target_wb.worksheets[0].title in ["Sheet", "Sheet1", "工作表1", "工作表", "模板"]:
                    # 如果只有一个模板工作表，直接使用它
                    target_ws = target_wb.active
                    target_ws.title = target_sheet_name
                    print(f"📋 已将模板工作表重命名为: {target_sheet_name}")
                else:
                    # 复制第一个工作表作为模板
                    template_ws = target_wb.worksheets[0]
                    target_ws = target_wb.copy_worksheet(template_ws)
                    target_ws.title = target_sheet_name
                    print(f"📋 已创建新工作表: {target_sheet_name}")

            # 修改A1标题
            new_title = f"{city_name}{county_name}2019-2025年高标准农田建设基本底数（二）"
            target_ws['A1'].value = new_title
            print(f"📝 已设置A1标题为: {new_title}")

            # 设置A6的值为区县名称+小计（而不是工作簿名称）
            a6_value = f"{county_name}小计"
            target_ws['A6'].value = a6_value
            print(f"📝 A6设置为区县名称+小计: {a6_value}")
            
            # 保存A7行的内容
            start_row = 7
            a7_content = None
            a7_merged_range = None

            # 查找A7的合并单元格
            for merged_range in target_ws.merged_cells.ranges:
                if (merged_range.min_row == start_row and merged_range.min_col == 1):
                    a7_merged_range = merged_range
                    a7_cell = target_ws.cell(row=start_row, column=1)
                    a7_content = a7_cell.value
                    target_ws.unmerge_cells(str(merged_range))

                    # 清除A7行的内容和格式
                    for col in range(1, 35):  # A到AH列
                        cell = target_ws.cell(row=start_row, column=col)
                        cell.value = None
                        cell.font = Font()
                        cell.alignment = Alignment()
                    break

            # 检查其他合并单元格
            merged_ranges_to_remove = []
            for merged_range in target_ws.merged_cells.ranges:
                if merged_range.min_row >= start_row and merged_range.min_row < start_row + len(data_rows):
                    merged_ranges_to_remove.append(merged_range)

            for merged_range in merged_ranges_to_remove:
                target_ws.unmerge_cells(str(merged_range))

            # 创建样式
            fill_color = PatternFill(start_color="E2EFDA", end_color="E2EFDA", fill_type="solid")
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            # 从A7开始填充数据
            print(f"📝 开始从第 {start_row} 行填充数据...")
            for i, row_data in enumerate(data_rows):
                current_row = start_row + i
                target_ws.row_dimensions[current_row].height = 46.80

                # 处理A-AH列
                for col_idx in range(1, 35):  # A到AH列
                    col_letter = get_column_letter(col_idx)
                    cell = target_ws[f"{col_letter}{current_row}"]

                    # 如果是A-I列，设置数据值
                    if col_idx <= 9 and col_idx - 1 < len(row_data):
                        value = row_data[col_idx - 1]
                        if value is not None:
                            cell.value = value

                    # 为所有A-AH列设置统一格式
                    try:
                        # 设置标准的数据格式
                        cell.font = Font(
                            name='宋体',
                            size=11,
                            bold=False
                        )
                        cell.alignment = Alignment(
                            horizontal='center',
                            vertical='center',
                            wrap_text=False
                        )

                        # 设置填充色
                        cell.fill = fill_color

                        # 设置内外边框
                        cell.border = thin_border

                    except Exception as format_error:
                        pass  # 忽略格式设置错误

            print(f"🎨 已为 {len(data_rows)} 行数据设置格式：行高20，A-AH列填充色#E2EFDA，内外边框")
            
            # 计算第6行的汇总值
            def safe_sum_column(col_letter):
                total = 0
                col_index = ord(col_letter) - ord('A')
                for row_data in data_rows:
                    if col_index < len(row_data):
                        value = row_data[col_index]
                        if value is not None:
                            try:
                                if isinstance(value, str):
                                    cleaned_value = value.replace(',', '').replace(' ', '')
                                    numeric_value = float(cleaned_value) if cleaned_value else 0
                                else:
                                    numeric_value = float(value)
                                total += numeric_value
                            except (ValueError, TypeError):
                                pass
                return total
            
            print("🧮 开始计算A6-H6的汇总值...")

            # 设置第6行的值
            target_ws['B6'].value = len(data_rows)  # 数据条数
            print(f"📊 B6设置为数据条数: {len(data_rows)}")

            e6_value = safe_sum_column('E')
            f6_value = safe_sum_column('F')
            g6_value = safe_sum_column('G')
            i6_value = safe_sum_column('I')

            target_ws['E6'].value = e6_value  # E列求和
            target_ws['F6'].value = f6_value  # F列求和
            target_ws['G6'].value = g6_value  # G列求和
            target_ws['I6'].value = i6_value  # I列求和

            print(f"💰 E6设置为E列求和: {e6_value}")
            print(f"💰 F6设置为F列求和: {f6_value}")
            print(f"💰 G6设置为G列求和: {g6_value}")
            print(f"💰 I6设置为I列求和: {i6_value}")

            # H6 = G6/F6*100
            if f6_value != 0:
                h6_value = round((g6_value / f6_value) * 100, 1)
            else:
                h6_value = 0.0
            target_ws['H6'].value = h6_value
            print(f"📈 H6设置为计算结果: {h6_value}% (G6/F6*100)")
            
            # 为第6行设置加粗格式
            for col_idx in range(1, 35):  # A到AH列
                col_letter = get_column_letter(col_idx)
                cell = target_ws[f"{col_letter}6"]
                current_font = cell.font
                cell.font = Font(
                    name=current_font.name or '宋体',
                    size=current_font.size or 11,
                    bold=True,
                    italic=current_font.italic,
                    underline=current_font.underline,
                    color=current_font.color
                )
            
            # 设置B列自适应宽度
            target_ws.column_dimensions['B'].auto_size = True
            print("📏 已设置B列为自适应宽度")

            # 将A7的内容放到数据的最后一行（区县算法也需要这个逻辑）
            if a7_content is not None:
                final_row = start_row + len(data_rows)
                if a7_merged_range:
                    new_range = f"A{final_row}:AH{final_row}"
                    target_ws.merge_cells(new_range)
                    target_ws[f"A{final_row}"].value = a7_content
                    print(f"📝 已将A7原内容放到第{final_row}行: {a7_content}")

                    # 设置格式（区县算法：黑体，加粗，11号）
                    try:
                        new_cell = target_ws[f"A{final_row}"]
                        new_cell.font = Font(name='黑体', size=11, bold=True)
                        new_cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
                        target_ws.row_dimensions[final_row].height = 77
                        print(f"🎨 已为第{final_row}行设置格式：黑体11号加粗，行高77")
                    except Exception as format_error:
                        print(f"⚠️ 格式设置失败: {format_error}")

            # 保存文件
            print(f"📋 最终工作表列表: {target_wb.sheetnames}")
            target_wb.save(output_path)
            target_wb.close()
            source_wb.close()
            
            return {
                'success': True,
                'message': '区县算法处理成功',
                'rows_processed': len(data_rows)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f"区县算法处理失败: {e}",
                'rows_processed': 0
            }

    def process_township_algorithm(self, source_path, template_path, output_path):
        """乡镇算法：处理多个工作表"""
        try:
            print("🏘️ 开始乡镇算法处理...")

            # 验证输入文件
            if not os.path.exists(source_path):
                print(f"❌ 源文件不存在: {source_path}")
                return {'success': False, 'message': f"源文件不存在: {source_path}", 'rows_processed': 0}

            if not os.path.exists(template_path):
                print(f"❌ 模板文件不存在: {template_path}")
                return {'success': False, 'message': f"模板文件不存在: {template_path}", 'rows_processed': 0}

            print("✅ 文件路径验证通过")
            print(f"📖 正在读取源文件: {os.path.basename(source_path)}")
            source_wb = openpyxl.load_workbook(source_path, data_only=True)

            # 检查输出文件是否存在
            if os.path.exists(output_path):
                print(f"📂 输出文件已存在，将追加工作表: {output_path}")
                target_wb = openpyxl.load_workbook(output_path)
                print(f"📋 现有工作表: {target_wb.sheetnames}")
                
                # 如果目标文件中没有合适的模板工作表，需要从原始模板文件复制一个
                template_sheet_names = ["Sheet", "Sheet1", "工作表1", "工作表", "模板"]
                has_template = any(ws.title in template_sheet_names for ws in target_wb.worksheets)
                
                if not has_template:
                    # 从原始模板文件加载一个工作表作为模板
                    print("📋 目标文件中没有找到模板工作表，从原始模板文件创建一个")
                    template_wb = openpyxl.load_workbook(template_path)
                    original_template_ws = template_wb.active
                    
                    # 在目标工作簿中创建一个临时模板工作表
                    temp_template = target_wb.create_sheet("临时模板")
                    
                    # 复制模板内容到临时工作表
                    for row in original_template_ws.iter_rows():
                        for cell in row:
                            new_cell = temp_template.cell(row=cell.row, column=cell.column)
                            new_cell.value = cell.value
                            
                            # 安全地复制样式
                            if cell.has_style:
                                try:
                                    new_cell.font = Font(
                                        name=cell.font.name,
                                        size=cell.font.size,
                                        bold=cell.font.bold,
                                        italic=cell.font.italic,
                                        underline=cell.font.underline,
                                        color=cell.font.color
                                    )
                                    new_cell.alignment = Alignment(
                                        horizontal=cell.alignment.horizontal,
                                        vertical=cell.alignment.vertical,
                                        wrap_text=cell.alignment.wrap_text
                                    )
                                    if cell.fill.fill_type:
                                        new_cell.fill = PatternFill(
                                            fill_type=cell.fill.fill_type,
                                            start_color=cell.fill.start_color,
                                            end_color=cell.fill.end_color
                                        )
                                    if cell.border.left.style:
                                        new_cell.border = Border(
                                            left=Side(style=cell.border.left.style, color=cell.border.left.color),
                                            right=Side(style=cell.border.right.style, color=cell.border.right.color),
                                            top=Side(style=cell.border.top.style, color=cell.border.top.color),
                                            bottom=Side(style=cell.border.bottom.style, color=cell.border.bottom.color)
                                        )
                                    new_cell.number_format = cell.number_format
                                except Exception as style_error:
                                    # 如果样式复制失败，只复制值
                                    pass
                    
                    # 复制合并单元格
                    for merged_range in original_template_ws.merged_cells.ranges:
                        temp_template.merge_cells(str(merged_range))
                    
                    # 复制行高和列宽
                    for row_num, row_dim in original_template_ws.row_dimensions.items():
                        if row_dim.height:
                            temp_template.row_dimensions[row_num].height = row_dim.height
                    for col_letter, col_dim in original_template_ws.column_dimensions.items():
                        if col_dim.width:
                            temp_template.column_dimensions[col_letter].width = col_dim.width
                    
                    template_wb.close()
                    print("✅ 已创建临时模板工作表")
            else:
                # 创建输出目录
                output_dir = os.path.dirname(output_path)
                if output_dir and not os.path.exists(output_dir):
                    os.makedirs(output_dir)
                    print(f"✓ 创建输出目录: {output_dir}")

                # 复制模板文件到输出位置
                shutil.copy2(template_path, output_path)
                print(f"✓ 已复制模板文件到: {output_path}")
                target_wb = openpyxl.load_workbook(output_path)
                print(f"📋 模板工作表: {target_wb.sheetnames}")

            total_processed = 0

            # 找到要用作模板的工作表
            template_ws = None
            template_sheet_names = ["Sheet", "Sheet1", "工作表1", "工作表", "模板", "临时模板"]
            
            for ws in target_wb.worksheets:
                if ws.title in template_sheet_names:
                    template_ws = ws
                    print(f"📋 使用工作表 '{ws.title}' 作为模板")
                    break
            
            if template_ws is None:
                # 如果没有找到模板工作表，使用第一个工作表作为模板
                template_ws = target_wb.worksheets[0]
                print(f"📋 使用第一个工作表 '{template_ws.title}' 作为模板")

            # 处理源文件中的每个工作表
            print(f"📋 发现 {len(source_wb.sheetnames)} 个工作表")

            for sheet_name in source_wb.sheetnames:
                print(f"\n📋 处理工作表: {sheet_name}")
                source_ws = source_wb[sheet_name]

                # 检查目标文件中是否已存在同名工作表
                if sheet_name in target_wb.sheetnames:
                    print(f"⚠️  工作表 '{sheet_name}' 已存在，跳过处理")
                    continue

                # 在同一个工作簿内复制模板工作表
                new_ws = target_wb.copy_worksheet(template_ws)
                new_ws.title = sheet_name
                print(f"📋 已创建新工作表: {sheet_name} (基于模板工作表)")

                # 设置工作表标签颜色为#E2EFDA
                new_ws.sheet_properties.tabColor = "E2EFDA"
                print(f"🎨 已设置工作表 '{sheet_name}' 标签颜色为 #E2EFDA")

                # 处理这个工作表的数据
                rows_processed = self.process_single_township_sheet(source_ws, new_ws, sheet_name)
                total_processed += rows_processed
                print(f"✅ 工作表 '{sheet_name}' 处理完成，处理了 {rows_processed} 行数据")

            # 删除临时模板工作表
            if "临时模板" in target_wb.sheetnames and len(target_wb.worksheets) > 1:
                target_wb.remove(target_wb["临时模板"])
                print("🗑️  已删除临时模板工作表")

            # 删除其他不需要的模板工作表
            if len(target_wb.worksheets) > 1:
                sheets_to_remove = []
                template_sheet_names = ["Sheet", "Sheet1", "工作表1", "工作表", "模板"]

                for ws in target_wb.worksheets:
                    # 只删除默认的模板工作表名称，且不在源文件工作表列表中的，且不是汇总工作表
                    if (ws.title in template_sheet_names and
                        ws.title not in source_wb.sheetnames and
                        not ws.title.endswith("汇总")):
                        sheets_to_remove.append(ws)

                for sheet in sheets_to_remove:
                    target_wb.remove(sheet)
                    print(f"🗑️  已删除模板工作表: {sheet.title}")

                if not sheets_to_remove:
                    print(f"🔒 保留所有现有工作表 (可能包含区县算法创建的工作表)")

            # 保存文件
            print(f"📋 最终工作表列表: {target_wb.sheetnames}")
            target_wb.save(output_path)
            target_wb.close()
            source_wb.close()

            return {
                'success': True,
                'message': '乡镇算法处理成功',
                'rows_processed': total_processed
            }

        except Exception as e:
            return {
                'success': False,
                'message': f"乡镇算法处理失败: {e}",
                'rows_processed': 0
            }

    def process_single_township_sheet(self, source_ws, target_ws, township_name):
        """处理单个乡镇工作表"""
        # 读取A-I列的数据（从第2行开始，跳过第1行）
        data_rows = []
        max_row = source_ws.max_row

        for row in range(2, max_row + 1):  # 从第2行开始
            row_data = []
            has_data = False

            for col in range(1, 10):  # A-I列 (1-9)
                cell = source_ws.cell(row=row, column=col)
                cell_value = cell.value
                row_data.append(cell_value)

                if cell_value is not None and str(cell_value).strip():
                    has_data = True

            # 只添加有数据的行
            if has_data:
                data_rows.append(row_data)

        if not data_rows:
            return 0

        # 创建市级代码到名称的映射
        city_code_map = {
            '3201': '南京市', '3202': '无锡市', '3203': '徐州市', '3204': '常州市',
            '3205': '苏州市', '3206': '南通市', '3207': '连云港市', '3208': '淮安市',
            '3209': '盐城市', '3210': '扬州市', '3211': '镇江市', '3212': '泰州市',
            '3213': '宿迁市'
        }

        # 初始化区县名称和代码（无默认值）
        county_name = None
        city_name = None

        # 从源文件第一行（表头）识别列名
        header_row = []
        for col in range(1, 12):  # A-K列（扩展到K列）
            cell = source_ws.cell(row=1, column=col)
            header_value = cell.value
            header_row.append(str(header_value).strip() if header_value else "")

        print(f"🔍 源文件表头: {header_row}")

        # 查找区县名称列和区县代码列的索引（明确指定J列和K列）
        county_name_col_idx = 10  # K列（第11列，索引为10）
        county_code_col_idx = 9   # J列（第10列，索引为9）
        
        print(f"🏘️ 使用K列作为区县名称列: 第{county_name_col_idx+1}列")
        print(f"�️ 使用J列作为区县代码列: 第{county_code_col_idx+1}列")

        # 从数据中提取市县信息
        if data_rows:
            # 需要重新读取包含J、K列的数据
            extended_first_row = []
            for col in range(1, 12):  # A-K列
                cell = source_ws.cell(row=2, column=col)  # 第2行数据
                extended_first_row.append(cell.value)

            # 从K列获取县名
            if county_name_col_idx < len(extended_first_row):
                name_value = extended_first_row[county_name_col_idx]
                if name_value and isinstance(name_value, str):
                    county_name = name_value.strip()
                    print(f"🏘️ 从K列获取县名: {county_name}")

            # 从J列获取市名
            if county_code_col_idx < len(extended_first_row):
                code_value = extended_first_row[county_code_col_idx]
                if code_value:
                    county_code = str(code_value).strip()
                    if len(county_code) >= 4:
                        city_code = county_code[:4]
                        if city_code in city_code_map:
                            city_name = city_code_map[city_code]
                            print(f"🏙️ 从J列区县代码 {county_code} 反推市名: {city_name}")

        # 验证是否成功获取到市县信息
        if not county_name or not city_name:
            print("⚠️ 警告：未能从数据中提取到完整的市县信息")
            if not county_name:
                print("❌ 缺少县名称信息")
            if not city_name:
                print("❌ 缺少市名称信息")
            # 对于乡镇算法，如果无法获取市县信息，使用乡镇名称作为标题
            county_name = ""
            city_name = ""

        print(f"🏙️ 最终市名称: {city_name}")
        print(f"🏘️ 最终县名称: {county_name}")

        # 修改A1标题（包含乡镇名称）
        new_title = f"{city_name}{county_name}{township_name}2019-2025年高标准农田建设基本底数（二）"
        target_ws['A1'].value = new_title

        # 设置A6的值为乡镇名称+小计
        a6_value = f"{township_name}小计"
        target_ws['A6'].value = a6_value
        print(f"📝 A6设置为乡镇名称+小计: {a6_value}")

        # 保存A7行的内容
        start_row = 7
        a7_content = None
        a7_merged_range = None

        # 查找A7的合并单元格
        for merged_range in target_ws.merged_cells.ranges:
            if (merged_range.min_row == start_row and merged_range.min_col == 1):
                a7_merged_range = merged_range
                a7_cell = target_ws.cell(row=start_row, column=1)
                a7_content = a7_cell.value
                target_ws.unmerge_cells(str(merged_range))

                # 清除A7行的内容和格式
                for col in range(1, 35):  # A到AH列
                    cell = target_ws.cell(row=start_row, column=col)
                    cell.value = None
                    cell.font = Font()
                    cell.alignment = Alignment()
                break

        # 检查其他合并单元格
        merged_ranges_to_remove = []
        for merged_range in target_ws.merged_cells.ranges:
            if merged_range.min_row >= start_row and merged_range.min_row < start_row + len(data_rows):
                merged_ranges_to_remove.append(merged_range)

        for merged_range in merged_ranges_to_remove:
            target_ws.unmerge_cells(str(merged_range))

        # 从A7开始填充数据（乡镇算法不需要填充色，但需要边框）
        # 创建边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # 创建无填充色的样式
        no_fill = PatternFill(fill_type=None)

        print(f"📝 开始从第 {start_row} 行填充数据（乡镇算法，无填充色）...")
        for i, row_data in enumerate(data_rows):
            current_row = start_row + i
            target_ws.row_dimensions[current_row].height = 46.80

            # 处理A-AH列
            for col_idx in range(1, 35):  # A到AH列
                col_letter = get_column_letter(col_idx)
                cell = target_ws[f"{col_letter}{current_row}"]

                # 乡镇算法的列对应关系调整：
                # 源A-C列 → 目标A-C列（不变）
                # 源E列 → 目标D列，源F列 → 目标E列，源G列 → 目标F列，源H列 → 目标G列，源I列 → 目标H列
                # （跳过源D列，从源E列开始向前移动1列）
                if col_idx <= 8:  # 目标表A-H列
                    source_col_idx = None

                    if col_idx <= 3:  # 目标A-C列对应源A-C列
                        source_col_idx = col_idx - 1  # 源数据索引（0-based）
                    elif col_idx >= 4 and col_idx <= 8:  # 目标D-H列对应源E-I列
                        source_col_idx = col_idx  # 源数据E-I列（索引4-8）

                    if source_col_idx is not None and source_col_idx < len(row_data):
                        value = row_data[source_col_idx]
                        if value is not None:
                            cell.value = value
                            # 调试输出列对应关系
                            source_col_letter = get_column_letter(source_col_idx + 1)
                            target_col_letter = get_column_letter(col_idx)
                            print(f"🔍 第{current_row}行：源{source_col_letter}列 → 目标{target_col_letter}列，值: {value}")

                # 设置标准格式（乡镇算法：宋体，10号，加粗，无填充色，有边框）
                try:
                    # 设置乡镇算法的数据格式：宋体，10号，加粗
                    cell.font = Font(
                        name='宋体',
                        size=10,
                        bold=True
                    )
                    cell.alignment = Alignment(
                        horizontal='center',
                        vertical='center',
                        wrap_text=False
                    )

                    # 明确清除填充色（乡镇算法不需要填充色）
                    cell.fill = no_fill

                    # 设置边框
                    cell.border = thin_border

                except Exception as format_error:
                    pass  # 忽略格式设置错误

        print(f"🎨 已为 {len(data_rows)} 行数据设置格式：宋体10号加粗，行高20，A-AH列无填充色，内外边框")

        # 计算第6行的汇总值（乡镇算法需要根据新的列对应关系计算）
        def safe_sum_column_township(target_col_letter):
            """乡镇算法的列汇总函数，根据目标列计算对应源列的汇总"""
            total = 0
            target_col_idx = ord(target_col_letter) - ord('A') + 1  # 1-based

            # 根据目标列确定源数据列索引
            source_col_idx = None
            if target_col_idx <= 3:  # 目标A-C列对应源A-C列
                source_col_idx = target_col_idx - 1  # 0-based
            elif target_col_idx >= 4 and target_col_idx <= 8:  # 目标D-H列对应源E-I列
                source_col_idx = target_col_idx  # 源E-I列（0-based索引4-8）

            if source_col_idx is not None:
                for row_data in data_rows:
                    if source_col_idx < len(row_data):
                        value = row_data[source_col_idx]
                        if value is not None:
                            try:
                                if isinstance(value, str):
                                    cleaned_value = value.replace(',', '').replace(' ', '')
                                    numeric_value = float(cleaned_value) if cleaned_value else 0
                                else:
                                    numeric_value = float(value)
                                total += numeric_value
                            except (ValueError, TypeError):
                                pass
            return total

        # 设置第6行的值
        target_ws['B6'].value = len(data_rows)  # 数据条数

        # 根据新的列对应关系计算汇总
        # 目标D列对应源E列，目标E列对应源F列，目标F列对应源G列，目标H列对应源I列
        d6_value = safe_sum_column_township('D')  # 源E列
        e6_value = safe_sum_column_township('E')  # 源F列
        f6_value = safe_sum_column_township('F')  # 源G列
        h6_value_sum = safe_sum_column_township('H')  # 源I列

        target_ws['D6'].value = d6_value  # 目标D列设置为源E列求和
        target_ws['E6'].value = e6_value  # 目标E列设置为源F列求和
        target_ws['F6'].value = f6_value  # 目标F列设置为源G列求和
        target_ws['H6'].value = h6_value_sum  # 目标H列设置为源I列求和

        print(f"💰 D6设置为源E列求和: {d6_value}")
        print(f"💰 E6设置为源F列求和: {e6_value}")
        print(f"💰 F6设置为源G列求和: {f6_value}")
        print(f"💰 H6设置为源I列求和: {h6_value_sum}")

        # G6 = F6/E6*100 (目标G列 = 目标F列/目标E列*100)
        if e6_value != 0:
            g6_value = round((f6_value / e6_value) * 100, 1)
        else:
            g6_value = 0.0

        target_ws['G6'].value = g6_value
        print(f"📈 G6设置为计算结果: {g6_value}% (F6/E6*100)")

        # 为第6行设置加粗格式
        for col_idx in range(1, 35):  # A到AH列
            col_letter = get_column_letter(col_idx)
            cell = target_ws[f"{col_letter}6"]
            current_font = cell.font
            cell.font = Font(
                name=current_font.name or '宋体',
                size=current_font.size or 11,
                bold=True,
                italic=current_font.italic,
                underline=current_font.underline,
                color=current_font.color
            )

        # 设置B列自适应宽度
        target_ws.column_dimensions['B'].auto_size = True
        print("📏 已设置B列为自适应宽度")

        # 将A7的内容放到数据的最后一行
        if a7_content is not None:
            final_row = start_row + len(data_rows)
            if a7_merged_range:
                new_range = f"A{final_row}:AH{final_row}"
                target_ws.merge_cells(new_range)
                target_ws[f"A{final_row}"].value = a7_content

                # 设置格式（乡镇算法：黑体，加粗，11号）
                try:
                    new_cell = target_ws[f"A{final_row}"]
                    new_cell.font = Font(name='黑体', size=11, bold=True)
                    new_cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
                    target_ws.row_dimensions[final_row].height = 77
                except:
                    pass

        return len(data_rows)





